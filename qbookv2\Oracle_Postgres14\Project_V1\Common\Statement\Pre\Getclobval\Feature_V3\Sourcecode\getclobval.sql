
  CREATE OR REPLACE PROCEDURE "WARDS"."P_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>DRAFTDETAILS" (ICLOB_DISCHARGEDRAFTDETAILS IN CLOB)
/*///////////////////////////////////////////////////////////////////////////////////

       File Description          : This procedure is to SAVE DISCHARGE DETAILS As Draft AND

       Description                :
       Parameters                :       ICLOB_DISCHARGEDRAFTDETAILS INPUT

       Returns                       :
   --------------------------------------------------------------------------------------------------------------------
       Date Created              : Dec 27,2008
       Author                       : <PERSON><PERSON>
   --------------------------------------------------------------------------------------------------------------------
        Change History
       Date Modified              :
       Changed By                :
       Change Description  :
           Version      :
  ///////////////////////////////////////////////////////////////////////////////////*/
 IS
  LXML_DISCHARGEDRAFTDETAILS XMLTYPE;
  /* V_INDEX                    XMLTYPE;
  I                          NUMBER(10) := 1;*/
  V_IPNUMBER         DISCHARGESUMMARYDRAFT.IPNUMBER%TYPE;
  V_DISCHARGEDRAFTNO DISCHARGESUMMARYDRAFT.DISCHARGEDRAFTNO%TYPE;
  v_LoginId          DISCHARGESUMMARYDRAFT.Createdby%TYPE;
  v_LocationId       DISCHARGESUMMARYDRAFT.Locationid%TYPE;
  V_ChiefComplaint   Temp_Chiefcomplaints.Chiefcomplaints%type;
  V_PresentIllness   Temp_Chiefcomplaints.Presentillnesshistory%type;
  V_PastFamily       Temp_Chiefcomplaints.Pastfamilyhistory%type;
  --V_COUNT                    NUMBER;
  V_DiscNo      Number;
  V_DSFinalized ADMISSIONDETAILS.Dsfinalized%TYPE;
BEGIN
  --insert into test values (ICLOB_DISCHARGEDRAFTDETAILS, sysdate);

  LXML_DISCHARGEDRAFTDETAILS := XMLTYPE(ICLOB_DISCHARGEDRAFTDETAILS);
  V_IPNUMBER                 := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@IPNumber')
                                .getstringval();
  v_LoginId                  := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@CreatedBy')
                                .getstringval();
  v_LocationId               := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@LocationID')
                                .getstringval();

  V_DSFinalized := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@DSFinalized')
                   .getstringval();

  SELECT COUNT(ds.rowid)
    INTO V_DiscNo
    FROM DISCHARGESUMMARYDRAFT DS
   WHERE DS.IPNUMBER = V_IPNUMBER
     AND DS.STATUS = 1;

  IF V_DiscNo > 0 THEN
    UPDATE DISCHARGESUMMARYDRAFT DS
       SET DS.STATUS = 0
     WHERE DS.IPNUMBER = V_IPNUMBER;
  END IF;
  /*INSERT INTO testnew
    (requestno, ipnumber)
  VALUES
    (v_DISCHARGEDRAFTNO, V_IPNUMBER);  */
  SELECT S_DISCHARGEDRAFTNO.NEXTVAL INTO V_DISCHARGEDRAFTNO FROM DUAL;
  INSERT INTO DISCHARGESUMMARYDRAFT
    (DISCHARGEDRAFTNO,
     DISCUSSION,
     CONDITIONATDISCHARGE,
     DIET,
     FOLLOWUPINSTRUCTION,
     PHYSICALACTIVITY,
     SPECIALINSTRUCTION,
     VITALSSTABLE,
     SURGICALWOUNDCLEAN,
     BLOODSUGARLEVELCONTROLLED,
     PAINSCOREBELOW4,
     NORAMLACTIVITIESCANBERESUMED,
     AMBULATORY,
     STATUS,
     CREATEDBY,
     CREATEDDATE,
     ACTIVITIESDURATIONTYPE,
     IPNUMBER,
     HYGIENE,
     LOCATIONID,
     DAMASTATUS,
     DAMACOMMENTS,
     POSTDISCHARGEINVESTIGATIONS,
     SUMMARYOFTREATMENT,
     REVIEWCONSULTANT,
     CONTACTINFORMATION,
     LABDETAILID,
     CHEIFCOMPLAINT,
     PRESENTILLNESSHISTORY,
     PASTFAMILYHISTORY,
     JUNIORDOCTOR,
     RADIODETAILID,
     PHYSIODETAILID,
     OTHERDETAILID,
     DORSTATUS,
     diagnosisdetails,
     EmergencyCare,
     PROCEDUREDETAILID,
     MMUSEDITEMSDETAILS)
  VALUES
    (v_DISCHARGEDRAFTNO,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Discussion'           )
     --.getstringval(),
     .getClobVal(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Conditionatdischarge'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Diet'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Followupinstruction'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Physicalactivity'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Specialinstruction'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Vitalsstable'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Surgicalwoundclean'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Bloodsugarlevelcontrolled'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Painscorebelow4'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Normalactivitiescanberesumed'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Ambulatory'           )
     .getstringval(),
     1,
     v_LoginId,
     SYSDATE,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@NormalActivitiesResumedUnits'           )
     .getstringval(),
     V_IPNUMBER,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Hygiene'           )
     .getstringval(),
     v_LocationId,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@DAMAStatus'           )
     .getNUMBERVAL(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@DAMAComments'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@PostDischargeInvestigations'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@SummaryOfTreatment'           )
     --.getstringval(),
     .getClobVal(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@ReviewConsultant'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@ContactInformation'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@LabTestIds'           )
     .getClobVal() /*.getstringval()*/,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Chiefcomplaints'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Historyofpresentillness'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Pastpresentfamilyhistory'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@JuniorDoctor'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@RadiologyIds'           )
     .getClobVal() /*.getstringval()*/,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@PhysioIds'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@OtherServicesIds'           )
     .getclobval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@DORStatus'           )
     .getNUMBERVAL(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/DiagnosisDetails/DiagnosisComments/@DiagnosisDetails'           )
     .getclobval()/*.getstringval()*/,
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@EmergencyCare'           )
     .getstringval(),
     EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@ProcedureIds'           )
     .getstringval(),
      EXTRACT           (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@MMItems'           )
     .getstringval());

  /*INSERT INTO temp_chiefcomplaints(Chiefcomplaints,PresentIllnessHistory,
  PastFamilyHistory,ipno)
       VALUES(EXTRACT(LXML_DISCHARGEDRAFTDETAILS,
                '/DischargeSummaryRequest/DischargeSummary/@Chiefcomplaints')
       .getstringval(),
       EXTRACT(LXML_DISCHARGEDRAFTDETAILS,
                '/DischargeSummaryRequest/DischargeSummary/@Historyofpresentillness')
       .getstringval(),
      EXTRACT(LXML_DISCHARGEDRAFTDETAILS,
                '/DischargeSummaryRequest/DischargeSummary/@Pastpresentfamilyhistory')
       .getstringval(),V_IPNUMBER );*/

  V_ChiefComplaint := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@Chiefcomplaints')
                      .getstringval();
  V_PresentIllness := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@Historyofpresentillness')
                      .getstringval();
  V_PastFamily     := LXML_DISCHARGEDRAFTDETAILS.EXTRACT('/DischargeSummaryRequest/DischargeSummary/@Pastpresentfamilyhistory')
                      .getstringval();

  /*SELECT COUNT(*)
   INTO V_COUNT
   FROM temp_chiefcomplaints t
  WHERE t.ipno = V_IPNUMBER;*/

  if V_ChiefComplaint is not null or V_PresentIllness is not null or
     V_PastFamily is not null then
    INSERT INTO temp_chiefcomplaints
      (Chiefcomplaints, PresentIllnessHistory, PastFamilyHistory, ipno)
    VALUES
      (EXTRACT   (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Chiefcomplaints'   )
       .getstringval(),
       EXTRACT   (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Historyofpresentillness'   )
       .getstringval(),
       EXTRACT   (LXML_DISCHARGEDRAFTDETAILS, '/DischargeSummaryRequest/DischargeSummary/@Pastpresentfamilyhistory'   )
       .getstringval(),
       V_IPNUMBER);
  END IF;
  /*IF V_COUNT = 0 THEN

  ELSE
      UPDATE temp_chiefcomplaints t
         SET t.chiefcomplaints       = V_ChiefComplaint,
             t.presentillnesshistory = V_PresentIllness,
             t.pastfamilyhistory     = V_PastFamily
       WHERE t.ipno = V_IPNUMBER;
  END IF;*/
  -- Discharge process change
  IF V_DSFinalized = 1 THEN

    UPDATE ADMISSIONDETAILS AD
       SET AD.DSFINALIZED = 1,
           AD.DSFINALIZEDON=sysdate,
           AD.DSFINALIZEDBY= v_LoginId
     WHERE AD.Ipnumber = V_IPNUMBER;

    UPDATE PRESCRIPTIONORDER PO
       SET PO.TRANSACTIONALSTATUS = 1,
           PO.Updatedby           = v_LoginId,
           PO.Updateddate         = SYSDATE
     WHERE PO.IPNUMBER = V_IPNUMBER
       AND PO.TRANSACTIONALSTATUS = 4
       AND PO.Status = 1;

    UPDATE Prescriptiondetails PD
       SET PD.TRANSACTIONALSTATUS = 1,
           PD.Updatedby           = v_LoginId,
           PD.Updateddate         = SYSDATE
     WHERE PD.IPNUMBER = V_IPNUMBER
       AND PD.TRANSACTIONALSTATUS = 4
       AND PD.STATUS = 1;

  END IF;

  /*EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE(sqlerrm);*/
END P_SAVEDISCHARGEDRAFTDETAILS;





