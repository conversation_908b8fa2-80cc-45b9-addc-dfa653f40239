
  CREATE OR REPLACE PROCEDURE "WARDS"."P_VIEWLOVMASTER" 
(
  in_LOVId in LOVMaster.Lovid%type:=null,
  in_LOVCode in LOVMaster. Lovcode%type:=null,
  in_LOVName in LOVMaster.Lovname%type:=null,
  in_LOVDescription in LOVMaster.Lovdescription%type:=null,
  oref_RequestCursor Out Sys_RefCursor
 )
 is
/*///////////////////////////////////////////////////////////////////////////////////////
//
//	File Description		: This procedure is retrieve the records
                          from RaiseRequests And RequesTests
//	Description    		  :
//	Parameters	      	:	in_LOVid           INPUT
                          in_LOVcode         INPUT
                          in_LOVName         INPUT
                          LOVDescription     INPUT
                          oref_RequestCursor  OUTPUT

//	Returns			        :
// --------------------------------------------------------------------------------------------------------------------
//	Date Created		    :
//	Author			        :  Tata Consultancy Services LTD
// --------------------------------------------------------------------------------------------------------------------
// 	Change History
//	Date Modified		:	 Month DD, YYYY (e.g. Sep 08, 2006)
//	Changed By		:
//	Change Description       :
//         Version                        :
///////////////////////////////////////////////////////////////////////////////////*/
v_Query LONG;
v_Query_WhereCondition LONG;
begin

  IF in_LovID IS NULL THEN
     v_Query:='Select l.LOVID,l.LOVCODE,l.LOVNAME ,l.LOVDESCRIPTION,l.createdby,l.approved From LOVMASTER l where status = 1 ';
     IF in_LOVCode IS NULL THEN
        v_Query_WhereCondition:=v_Query_WhereCondition||' ';
     ELSE
        v_Query_WhereCondition:=v_Query_WhereCondition||' and Upper(LovCode) Like upper('''||in_LOVCode||''') || ''%''';
     END IF;
     IF in_Lovname IS NULL THEN
        v_Query_WhereCondition:=v_Query_WhereCondition||' ';
     ELSE
        v_Query_WhereCondition:=v_Query_WhereCondition||' and Upper(LovName) Like upper('''||in_Lovname||''') || ''%''';
     END IF;
     IF in_LOVDescription IS NULL THEN
        v_Query_WhereCondition:=v_Query_WhereCondition||' ';
     ELSE
        v_Query_WhereCondition:=v_Query_WhereCondition||' and Upper(LOVDESCRIPTION) Like upper('''||in_LOVDescription||''') || ''%''';
     END IF;

     --Dbms_Output.Put_Line (v_Query);
     v_Query:=v_Query||v_Query_WhereCondition;
  ELSE
      v_Query:='Select  l.LOVID,l.LOVCODE,l.LOVNAME ,l.LOVDESCRIPTION,l.createdby,l.approved From LOVMASTER l   where   Status = 1 and lovid='||in_LovID;
  END IF;

  OPEN   oref_RequestCursor FOR v_Query;

 /* IF in_LovID IS NULL THEN
  OPEN   oref_RequestCursor FOR
  Select l.LOVID,l.LOVCODE,l.LOVNAME ,l.LOVDESCRIPTION,l.createdby,l.approved From LOVMASTER l
  where  (in_LOVCode Is null or Upper(LovCode) Like upper(in_LOVCode) || '%')
  and    (in_Lovname Is null or Upper(LovName) Like upper(in_LovName) || '%')
  and    (in_LOVDescription Is null or Upper(LOVDESCRIPTION) Like upper(in_LOVDescription) || '%')
  and     status = 1;

   else

   OPEN    oref_RequestCursor For
   Select  l.LOVID,l.LOVCODE,l.LOVNAME ,l.LOVDESCRIPTION,l.createdby,l.approved From LOVMASTER l
   where   Status = 1 and lovid=in_LovID;
   End IF;*/
   Exception
   When Others Then
   Dbms_Output.Put_Line (SqlCode || Sqlerrm);
 end P_VIEWLOVMASTER;



 



