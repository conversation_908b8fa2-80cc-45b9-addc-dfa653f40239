create procedure p_name()$$ language plpgsql 
update BILLING.GROOMGST_CREDIT_INTERIM D set D.REFTARIFF = I.REFTARIFF 
from (
select distinct (
select ITS.SERVICEID,
 ITS.REQUESTNO_NEW,
 ITS.REFTARIFF AS REFTARIFF 
from BILLING.INTERIMBILL I,
 BILLING.INTERIMBILLSERVICES ITS 
where I.INTERIMBILLNO = IV_BILLNO 
and I.INTERIMBILLID = ITS.INTERIMBILLNO 
and ITS.REFTARIFF <> 0 )I 
where I.REQUESTNO_NEW = REPLACE(REPLACE(D.REQUESTNO,
')',
''),
'(',
'') 
and I.SERVICEID = D.SERVICEID 
and BASEDERIVEDRELATION >0 
and D.REFTARIFF = 0;
 end;
$$;