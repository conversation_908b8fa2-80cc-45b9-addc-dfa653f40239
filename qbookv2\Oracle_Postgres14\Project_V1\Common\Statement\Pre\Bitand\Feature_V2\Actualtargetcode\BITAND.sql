create or replace procedure sample()

select public.bitand(-1,-1)  from dual 

select public.bitand(1,-1)  from dual 

select public.bitand(-1,1)  from dual 

select public.bitand(1.7,1)  from dual 

select public.bitand(1,1.7)  from dual 

select public.bitand(1.7,1.7)  from dual 

select public.bitand(2.8,1.6)  from dual  

select public.bitand(82,16)  from dual 

select public.bitand(1,1)  from dual


end     ;