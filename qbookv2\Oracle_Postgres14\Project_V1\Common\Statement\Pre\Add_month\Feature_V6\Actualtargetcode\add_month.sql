create or replace procedure example1(
  out ecount int)
is
declare 
V_FROMDATE date;

begin

TRUNC(add_months(Last_day(sysdate)  +  1,  - 2)) AND TRUNC(add_months(last_day(sysdate),  - 1));

TRUNC(add_months(Last_day(sysdate)  +  1,  - 2)) AND TRUNC(add_months(last_day(sysdate),  - 1)) AND X.LUXURYTAX>0

TRUNC(add_months(Last_day(sysdate)  +  1,  - 2)) AND TRUNC(add_months(last_day(sysdate),  - 1)) UNION ALL

end;