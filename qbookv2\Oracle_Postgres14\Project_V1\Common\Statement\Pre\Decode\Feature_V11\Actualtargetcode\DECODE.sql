create or replace procedure pr_decode_1(ref out sys_refcursor)
is
begin 

open ref for select (case deptno when 10 then 100 when 20 then 200 else 300 end) from emp;
end;


create or replace procedure pr_decode_naga(ref out sys_refcursor)
is
begin 

open ref for select (case deptno when 10 then 100 when 20 then 200 else 300 end) from emp;
end;


create or replace procedure pr_decode 
is 
begin
select
      id,
      name,
	  result,
	  (case result when 'p' then 'pass' when 'f' then 'fail' else 'not available' end)as status
from
student;
end;