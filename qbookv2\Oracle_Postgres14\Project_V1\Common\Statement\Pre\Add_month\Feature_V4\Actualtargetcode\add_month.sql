create or replace procedure example1(
  out ecount int)
is
declare 
V_FROMDATE date;

begin

TRUNC(Last_day(sysdate) + (1 || 'days')::interval - (2 || 'month')::interval) AND TRUNC(last_day(sysdate) - (1 || 'month')::interval);

TRUNC(Last_day(sysdate) + (1 || 'days')::interval - (2 || 'month')::interval) AND TRUNC(last_day(sysdate) - (1 || 'month')::interval) AND X.LUXURYTAX>0

TRUNC(Last_day(sysdate) + (1 || 'days')::interval - (2 || 'month')::interval) AND TRUNC(last_day(sysdate) - (1 || 'month')::interval) UNION ALL

end;