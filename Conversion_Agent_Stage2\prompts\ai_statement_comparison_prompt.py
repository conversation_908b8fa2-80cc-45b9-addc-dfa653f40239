"""
AI Statement Comparison Prompt for Stage 2 Processing.

This module creates prompts for AI-driven comparison of AI corrected output vs applied modules output
to determine if they have similar target functionality.
"""

def create_ai_statement_comparison_prompt(
    ai_corrected_statement: str,
    applied_modules_statement: str,
    db_terms: dict
) -> str:
    """
    Create a simplified prompt for AI-driven statement comparison.

    Args:
        ai_corrected_statement: AI corrected PostgreSQL statement from Stage 1
        applied_modules_statement: Statement after applying updated modules
        db_terms: Database-specific terminology

    Returns:
        str: Simplified comparison prompt for AI analysis
    """

    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    prompt = f"""
You are a SENIOR {expert_title} with expertise in {target_db} statement comparison.

CORE TASK: Identify ONLY functional differences that affect SQL execution behavior.

Your task is to determine if two {target_db} statements would produce the same results when executed, regardless of comments, formatting, or visual differences.

WHAT TO REPORT AS DIFFERENCES:
✅ Different SQL keywords or functions that change behavior
✅ Different clause ordering that affects execution sequence
✅ Missing or extra SQL operations or logic
✅ Different data processing approaches that yield different results

WHAT TO NEVER REPORT AS DIFFERENCES:
❌ Comments (-- or /* */) - these never affect SQL execution
❌ Whitespace, tabs, line breaks, or indentation differences
❌ Case differences (SELECT vs select)
❌ Visual arrangement that doesn't change functional logic
❌ Formatting variations that don't impact execution behavior

DECISION RULE:
If both statements would produce the same results when executed by the database engine, they are functionally equivalent (return true).

COMPARISON CONTEXT:
==================
**Target Database**: {target_db}

**AI Corrected {target_db} Statement** (Expected):
{ai_corrected_statement}

**Applied Modules {target_db} Statement** (Actual):
{applied_modules_statement}

FUNCTIONAL COMPARISON FOCUS:
===========================

1. **SQL EXECUTION BEHAVIOR**:
   - Do both statements execute the same SQL operations?
   - Do they process data in the same logical sequence?
   - Would they produce identical results when run?

2. **CRITICAL FUNCTIONAL DIFFERENCES**:
   - Different SQL keywords that change execution behavior
   - Different clause ordering that affects SQL execution sequence
   - Missing or extra SQL operations or logic blocks
   - Different data processing approaches that yield different results
   - Different SQL functions or operations that change outcomes

3. **NON-FUNCTIONAL ELEMENTS (IGNORE COMPLETELY)**:
   - Comments are never functional differences - they don't affect execution
   - Whitespace and formatting never change SQL behavior
   - Case differences don't affect SQL execution
   - Visual presentation doesn't impact database processing

COMPARISON METHODOLOGY:
======================

**STEP 1: FUNCTIONAL ANALYSIS**
- Mentally ignore all comments, whitespace, and formatting differences
- Focus only on SQL keywords, functions, and logical structure
- Identify the core SQL operations in each statement
- Map the logical flow and execution sequence

**STEP 2: EXECUTION BEHAVIOR COMPARISON**
- Compare the SQL operations that would actually execute
- Check if both statements would process data in the same way
- Verify that the logical sequence of operations is identical
- Identify any differences in SQL execution behavior

**STEP 3: RESULT EQUIVALENCE CHECK**
- Determine if both statements would produce the same output
- Check if the data processing logic is functionally equivalent
- Verify that the execution results would be identical

FUNCTIONAL EQUIVALENCE GUIDELINES:
==================================

**FUNCTIONALLY EQUIVALENT STATEMENTS** have:
- Same core SQL operations and execution logic
- Same data processing approach and results
- Same logical sequence of SQL operations
- Identical functional behavior when executed

**FUNCTIONALLY DIFFERENT STATEMENTS** have:
- Different SQL operations or functions that change behavior
- Different data processing logic that yields different results
- Different execution sequence that affects outcomes
- Missing or extra SQL functionality that impacts results

DECISION CRITERIA:
=================

**STATEMENTS MATCH** (return true):
✅ Both statements would produce identical results when executed
✅ Same SQL operations and logical processing sequence
✅ Same functional behavior and data processing approach
✅ Only comments, formatting, whitespace, or case differences exist
✅ **CRITICAL: Comments never affect SQL execution - ignore all comment differences**

**STATEMENTS DON'T MATCH** (return false):
❌ Different SQL operations or functions that change execution behavior
❌ Missing or extra SQL functionality that affects results
❌ Different logical processing sequence that impacts outcomes
❌ Different data processing approaches that yield different results

OUTPUT FORMAT (JSON):
====================
{{
  "statements_match": <boolean - true if statements are functionally equivalent, false if they have functional differences that affect execution>,
  "explanation": "<List only the functional differences that affect SQL execution behavior. Do not mention comments, formatting, or whitespace differences. Focus on SQL operations, keywords, and logical differences that would change execution results.>",
  "transformation_guidance": {{
    "required": <boolean - true if transformation is needed>,
    "specific_changes": [
      {{
        "source_pattern": "<Current pattern that needs to change>",
        "target_pattern": "<What it should become>",
        "transformation_method": "<How to implement the change>",
        "variations_to_handle": ["<variation1>", "<variation2>", "<variation3>"]
      }}
    ],
    "implementation_steps": [
      "<Step 1: Specific action>",
      "<Step 2: Specific action>",
      "<Step 3: Specific action>"
    ],
    "parameter_mapping": {{
      "extraction": "<How to extract variable components>",
      "reconstruction": "<How to reconstruct in target format>"
    }}
  }}
}}

COMPARISON FOCUS:
================
- **Functional Logic**: Focus on SQL operations that actually execute
- **Execution Behavior**: Compare what the database engine would actually do
- **Result Equivalence**: Determine if both statements would produce the same results
- **Ignore Non-Functional Elements**: Comments, formatting, and whitespace never affect execution

FUNCTIONAL COMPARISON EXAMPLES:
==============================
Example 1 - FUNCTIONALLY EQUIVALENT:
Statement A: "SELECT col1 FROM table WHERE id = 1 ORDER BY col1 LIMIT 5; -- this is a comment"
Statement B: "select col1 from table where id = 1 order by col1 limit 5;"
Analysis: Same SQL operations, same execution sequence. Comments don't affect execution. EQUIVALENT.

Example 2 - FUNCTIONALLY DIFFERENT:
Statement A: "SELECT col1 FROM table WHERE id = 1 ORDER BY col1 LIMIT 5;"
Statement B: "SELECT col1 FROM table WHERE id = 1 LIMIT 5 ORDER BY col1;"
Analysis: Different clause positioning (ORDER BY vs LIMIT sequence) affects execution. NOT EQUIVALENT.

Example 3 - FUNCTIONALLY EQUIVALENT (ignore comments):
Statement A: "BEGIN SELECT col1 FROM table; END;"
Statement B: "BEGIN SELECT col1 FROM table; -- commented condition END;"
Analysis: Same functional SQL operations. Comments don't change execution behavior. EQUIVALENT.

ANALYSIS REQUIREMENTS:
=====================
1. **FUNCTIONAL FOCUS**: Identify only differences that affect SQL execution behavior
2. **IGNORE NON-FUNCTIONAL ELEMENTS**: Never report comments, formatting, or whitespace as differences
3. **EXECUTION IMPACT**: Explain how each functional difference would change execution results
4. **CLEAR CATEGORIZATION**: Distinguish between functional differences and non-functional formatting
5. **ACTIONABLE FEEDBACK**: If statements don't match, provide specific guidance for functional changes:
   - Identify exact SQL operations that need to change
   - Specify target SQL patterns they should become
   - Focus on functional transformations, not formatting changes
   - Provide implementation guidance for SQL logic changes
"""

    return prompt
