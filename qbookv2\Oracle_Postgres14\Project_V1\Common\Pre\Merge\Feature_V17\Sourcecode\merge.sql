
create procedure p_name()$$
language plpgsql
MERGE INTO BILLING.GROOMGST_CREDIT_INTERIM D
    USING
    (
        SELECT
            ITS.SERVICEID, ITS.REQUESTNO_NEW , ITS.REFTARIFF AS REFTARIFF
        FROM
            BILLING.INTERIMBILL I, BILLING.INTERIMBILLSERVICES ITS
        WHERE I.INTERIMBILLNO = IV_BILLNO
        AND I.INTERIMBILLID = ITS.INTERIMBILLNO
        AND ITS.REFTARIFF <> 0
    )I
    ON
    (
        I.REQUESTNO_NEW = REPLACE(REPLACE(D.REQUESTNO,')',''),'(','') AND
        I.SERVICEID = D.SERVICEID
    )
    WHEN MATCHED THEN
    UPDATE SET
        D.REFTARIFF = I.REFTARIFF
    WHERE BASEDERIVEDRELATION >0
    AND D.REFTARIFF = 0;
 end;$$;