create or replace procedure pr_decode_1(ref out sys_refcursor)
is
begin 

open ref for select decode(deptno,10,100,20,200,300) from emp;
end;


create or replace procedure pr_decode_naga(ref out sys_refcursor)
is
begin 

open ref for select decode(deptno,10,100,20,200,300) from emp;
end;


create or replace procedure pr_decode 
is 
begin
select
      id,
      name,
	  result,
	  decode  (result,'p','pass','f','fail','not available')as status
from
student;
end;