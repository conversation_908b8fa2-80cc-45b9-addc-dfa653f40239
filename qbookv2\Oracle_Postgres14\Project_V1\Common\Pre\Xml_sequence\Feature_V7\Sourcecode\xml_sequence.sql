
 CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE "WARDS"."P_UPDATEQUANTITY" (ICLOB_PRESCDTLS IN CLOB) IS
  V_PRESCDTLS    XMLTYPE;
  V_ACTUALDOSAGE VARCHAR2(30);
  X              NUMBER;
  --Classic Emaar change to update unit quantity when updated while issuing in pharmacy
BEGIN
  /*V_PRESCDTLS := XMLTYPE(ICLOB_PRESCDTLS);
  SELECT PD.DOSAGE
    INTO V_ACTUALDOSAGE
    FROM WARDS.PRESCRIPTIONDETAILS PD
   WHERE PD.PRESCRIPTIONDETAILID = (case when (select unnest(xpath('/UQDetails/@PrescDtlID',V_PRESCDTLS)))::text='' then null
else (select unnest(xpath('/UQDetails/@PrescDtlID',V_PRESCDTLS)))::text end)::numeric;
  UPDATE WARDS.PRESCRIPTIONDETAILS PD
     SET PD.DOSAGE          = (case when (select unnest(xpath('/UQDetails/@UnitQty',V_PRESCDTLS)))::text='' then null
else (select unnest(xpath('/UQDetails/@UnitQty',V_PRESCDTLS)))::text end)
,PD.DILUENTQUANTITY = V_ACTUALDOSAGE
   WHERE PD.PRESCRIPTIONDETAILID = (case when (select unnest(xpath('/UQDetails/@PrescDtlID',V_PRESCDTLS)))::text='' then null
else (select unnest(xpath('/UQDetails/@PrescDtlID',V_PRESCDTLS)))::text end)::numeric;*/

  FOR X IN (SELECT 
(case when (select unnest(xpath('UQDetails/@PrescDtlID',VALUE(li))))::text='' then null
else (select unnest(xpath('UQDetails/@PrescDtlID',VALUE(li))))::text end 
) AS PrescDtlID,
(case when (select unnest(xpath('UQDetails/@UnitQty',VALUE(li))))::text='' then null
else (select unnest(xpath('UQDetails/@UnitQty',VALUE(li))))::text end 
) AS UnitQty
                           FROM (with ctc as (
(select unnest(xpath('UpdateDetails/UQDetails',xml('<UpdateDetails>' ||
                                                     ICLOB_PRESCDTLS ||
                                                     '</UpdateDetails>'))) as value ) select value from ctc 
													 
													 
													 )) li) LOOP
    BEGIN
      SELECT PD.DOSAGE
        INTO V_ACTUALDOSAGE
        FROM WARDS.PRESCRIPTIONDETAILS PD
       WHERE PD.PRESCRIPTIONDETAILID = X.PrescDtlID;

      UPDATE WARDS.PRESCRIPTIONDETAILS PD
         SET PD.DOSAGE = decode(X.UnitQty,'',PD.DOSAGE,' ',PD.DOSAGE,'0',PD.DOSAGE,X.UnitQty),PD.DILUENTQUANTITY = V_ACTUALDOSAGE
       WHERE PD.PRESCRIPTIONDETAILID = X.PrescDtlID;
    EXCEPTION
      WHEN OTHERS THEN
        NULL;
    END;
  END LOOP;

END P_UPDATEQUANTITY;

