CREATE OR REPLACE PROCEDURE PR_DECODE
IS
BEGIN
SELECT
ID,
NAME,
RESULT,
CASE RESULT 
WHEN 'P' THEN 'PASS' 
WHEN 'F' THEN 'FAIL' 
ELSE 'NOT AVAILABLE' 
END
AS STATUS
FROM
STUDENT;
END;




create or replace procedure pr_decode_1(ref out sys_refcursor)
is
begin 

open ref for select case when deptno=10 then 100 
            when deptno=20 then 200
       else 
         300     
       end      
from emp; 
end;